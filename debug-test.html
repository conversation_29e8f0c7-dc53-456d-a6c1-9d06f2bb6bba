<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Smart Autofill Assistant</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.2s;
            box-sizing: border-box;
        }
        
        input:focus, textarea:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .submit-btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .submit-btn:hover {
            background: #0056b3;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>Debug Test Form</h1>
        <p>This is a simple test form to debug the "No analysis data received from content script" issue.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" placeholder="Enter your email" required>
            </div>
            
            <div class="form-group">
                <label for="fullName">Full Name</label>
                <input type="text" id="fullName" name="fullName" placeholder="Enter your full name" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone" placeholder="Enter your phone number">
            </div>
            
            <div class="form-group">
                <label for="address">Address</label>
                <textarea id="address" name="address" placeholder="Enter your address" rows="3"></textarea>
            </div>
            
            <button type="submit" class="submit-btn">Submit</button>
        </form>
        
        <div class="debug-info">
            <strong>Debug Instructions:</strong><br>
            1. Open the Smart Autofill Assistant extension<br>
            2. Create a test profile with sample data (e.g., "Name: John Doe, Phone: 13800138000, Email: <EMAIL>")<br>
            3. Click "Start Autofill" to test the complete workflow<br>
            4. Check the browser console for detailed debug logs<br>
            5. If using Gemini API, make sure your API key is configured in Settings<br><br>
            
            <strong>Expected Behavior:</strong><br>
            - Extension should detect 4 form fields<br>
            - Analysis should complete without "No analysis data received" error<br>
            - Form fields should be filled with appropriate data<br>
            - Progress should be shown during the process
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Form submission prevented for testing purposes');
        });
        
        // Log form info for debugging
        console.log('Debug test page loaded');
        console.log('Forms found:', document.querySelectorAll('form').length);
        console.log('Input fields found:', document.querySelectorAll('input, textarea').length);
        
        // Add some debugging helpers
        window.debugAutofill = {
            getPageData: function() {
                return {
                    url: window.location.href,
                    title: document.title,
                    forms: Array.from(document.querySelectorAll('form')).map((form, index) => ({
                        index: index,
                        action: form.action || '',
                        method: form.method || 'get',
                        fields: Array.from(form.querySelectorAll('input, textarea')).map(field => ({
                            type: field.type,
                            name: field.name,
                            id: field.id,
                            placeholder: field.placeholder,
                            required: field.required
                        }))
                    }))
                };
            },
            
            testContentScript: function() {
                if (typeof chrome !== 'undefined' && chrome.runtime) {
                    console.log('Chrome extension context available');
                    return true;
                } else {
                    console.log('Chrome extension context not available - open this page in a tab with the extension loaded');
                    return false;
                }
            }
        };
        
        console.log('Debug helpers available: window.debugAutofill');
    </script>
</body>
</html>